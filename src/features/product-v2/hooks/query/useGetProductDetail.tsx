import { Params } from '@/types/http.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { productV2QueryKeys } from './queryKeys'
import { productV2Service } from '../../services/product-v2.service'
import { Product } from '@/payload-types'

type ProductDetailPropsConfig = Omit<UseQueryOptions<Product | null, Error>, 'queryFn' | 'queryKey'>

interface useGetProductDetailProps {
  id?: string
  params?: Params
  options?: RequestInit
  key?: string | number
  config?: ProductDetailPropsConfig
}

/**
 * Custom hook for fetching product age groups data
 * @param params - Query parameters for filtering and pagination
 * @param options - Request initialization options
 * @param key - Optional cache key
 * @param config - React Query configuration options
 * @returns Object containing product age groups data and loading states
 */
export const useGetProductDetail = ({
  id,
  params = {},
  options = {},
  key,
  config = {},
}: useGetProductDetailProps = {}) => {
  const {
    isError: isGetProductDetailError,
    isPending: isGetProductDetailLoading,
    data: productDetail,
    ...rest
  } = useQuery({
    queryKey: [productV2QueryKeys['product-detail'].base(), params, key],
    enabled: !!id,
    queryFn: async () => {
      return productV2Service.getProductDetail({
        id: id as string,
        params,
        options,
      })
    },
    ...config,
  })

  return {
    isGetProductDetailError,
    isGetProductDetailLoading,
    productDetail,
    ...rest,
  }
}
