import { InformationIcon } from '@/components/Icons/InformationIcon'
import { ProductV2Content } from '@/features/product-v2/components/ProductDetail/ProductV2Content'
import { Product } from '@/payload-types'
import { useTranslations } from 'next-intl'
import { SerializedEditorState } from 'node_modules/lexical/LexicalEditorState'

import { ShareIcon, ShareLinkButton } from '@/components/ShareLinkButton/ShareLinkButton'
import { FavoriteProductButtonClient } from '@/features/product-v2/components/ProductDetail/FavoriteProductButton.client'
import { ProductV2 } from '@/features/product-v2/types'

type ProductDetailContentProps = {
  product: Product
}
const ProductDetailContent: React.FC<ProductDetailContentProps> = ({ product }) => {
  const t = useTranslations()
  const {
    title,
    id,
    isFavorite,
    uses,
    dosage,
    dosageForm,
    specification,
    ingredient,
    contraindications,
    note,
    jaTitle,
  } = (product as ProductV2) || {}

  return (
    <div className="flex flex-col gap-3">
      <div className="typo-heading-7">{product.title}</div>
      {jaTitle && <div className="typo-body-7 text-subdued">{jaTitle}</div>}
      <div className="flex items-center gap-3">
        <ShareLinkButton
          title={title}
          text={title}
          className="flex flex-1 items-center justify-end"
        >
          <div className="flex min-h-[36px] flex-1 items-center justify-center gap-3 rounded-[99px] bg-neutral-50 px-3 py-2">
            <ShareIcon />
            {t('MES-444')}
          </div>
        </ShareLinkButton>
        <div className="flex min-h-[36px] flex-1 items-center justify-center rounded-[99px] bg-neutral-50">
          <FavoriteProductButtonClient
            id={id}
            isFavorite={isFavorite ?? false}
            title={t('MES-140')}
          ></FavoriteProductButtonClient>
        </div>
      </div>
      <article key="detail-info" className="flex flex-col gap-3 rounded-lg bg-white p-3">
        <div className="flex items-center gap-3">
          <InformationIcon className="shrink-0"></InformationIcon>

          <div className="typo-body-3">{t('MES-490')}</div>
        </div>

        <ProductV2Content
          uses={uses as SerializedEditorState}
          dosage={dosage as SerializedEditorState}
          dosageForm={dosageForm as SerializedEditorState}
          specification={specification as SerializedEditorState}
          ingredient={ingredient as SerializedEditorState}
          contraindications={contraindications as SerializedEditorState}
          note={note as SerializedEditorState}
          isHiddenButtonShow={true}
          isShowFull={true}
        />
      </article>
    </div>
  )
}

export default ProductDetailContent
