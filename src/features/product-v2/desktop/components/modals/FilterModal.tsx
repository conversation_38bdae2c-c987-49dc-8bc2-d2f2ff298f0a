'use client'

import { useLocale, useTranslations } from 'next-intl'

import CloseIcon from '@/assets/icons/exit.svg'
import Image from 'next/image'
import { useEffect, useState } from 'react'
import { useGetProductAgeGroup } from '@/features/product-v2/hooks/query/useGetProductAgeGroup'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { useGetMedicineType } from '@/features/product-v2/hooks/query/useGetMedicineType'

import NoteIcon from '@/assets/icons/warning-circle-primary.svg'

import arrowDown from '@/assets/icons/arrow-down -gray.svg'
import { Media, MedicineType, ProductAgeGroup } from '@/payload-types'
import { Button } from '@/components/ui/Button/Button'

export type IFilterProduct = {
  params: MedicineTypeExtend[] | ProductAgeGroupExtend[]
  type: 'default' | 'filter'
}

export type MedicineTypeExtend = MedicineType & {
  type: 'medicine' | 'age' | 'remove'
}

export type ProductAgeGroupExtend = ProductAgeGroup & {
  type: 'medicine' | 'age' | 'remove'
}

type FilterModalProps = {
  isMedicine?: boolean
  close: () => void
  applyFilter: (data: IFilterProduct) => void
  filterOptions: IFilterProduct['params']
}

const FilterModal: React.FC<FilterModalProps> = ({
  isMedicine = false,
  filterOptions,
  close,
  applyFilter,
}) => {
  const locale = useLocale()

  const t = useTranslations()

  const [selectedCategories, setSelectedCategories] = useState<
    Record<string, MedicineTypeExtend | ProductAgeGroupExtend>
  >({})

  const { productAgeGroups, isGetProductAgeGroupLoading } = useGetProductAgeGroup()

  const [isExpandNote, setIsExpandNote] = useState<boolean>(false)

  useEffect(() => {
    const initialSelected: Record<string, MedicineTypeExtend | ProductAgeGroupExtend> = {}
    filterOptions.forEach((item) => {
      initialSelected[item.id] = item
    })
    setSelectedCategories(initialSelected)
  }, [filterOptions])

  const { medicineType, isGetMedicineTypeLoading } = useGetMedicineType({
    params: {
      depth: 1,
      limit: 20,
      locale: locale ?? 'vi',
    },

    config: {
      staleTime: 5 * 60 * 1000,
      enabled: isMedicine,
    },
  })

  const handleSelectCategory = (
    category: MedicineType | ProductAgeGroup,
    type: 'medicine' | 'age' | 'remove',
  ) => {
    setSelectedCategories((prev) => {
      const newSelected = { ...prev }
      if (newSelected[category.id]) {
        delete newSelected[category.id]
      } else {
        newSelected[category.id] = {
          ...category,
          type,
        } as MedicineTypeExtend | ProductAgeGroupExtend
      }
      return newSelected
    })
  }

  const handleApplyFilter = (type: 'default' | 'filter') => {
    const categoriesSelected = Object.values(selectedCategories)

    applyFilter({ params: type === 'filter' ? categoriesSelected : [], type })
  }

  return (
    <div className="flex flex-col gap-3 bg-white p-6">
      <div className="flex items-center justify-between py-2">
        <span className="typo-heading-7 text-primary-500">{t('MES-743')}</span>

        <Image onClick={close} className="size-6 cursor-pointer" src={CloseIcon} alt={'close'} />
      </div>

      <div className="flex flex-1 flex-col gap-3 overflow-auto">
        <div className="flex flex-wrap items-center gap-3">
          <span className="typo-body-7 text-subdued">
            {t('MES-706')} ({Object.keys(selectedCategories).length}):
          </span>
          <div className="flex flex-wrap items-center gap-2">
            {Object.values(selectedCategories).map((category) => (
              <div
                key={category.id}
                className="flex items-center gap-3 rounded-md border border-primary-500 px-2 py-1 text-primary-500"
              >
                {category.title}
                <Image
                  className="cursor-pointer"
                  src={CloseIcon}
                  alt="remove"
                  height={16}
                  width={16}
                  onClick={() => handleSelectCategory(category, 'remove')}
                />
              </div>
            ))}
          </div>
        </div>

        {/* Độ tuổi sử dụng */}
        <div className="">
          <div className="typo-body-3">{t('MES-707')}</div>

          <div className="mt-4 grid grid-cols-3 gap-3">
            {isGetProductAgeGroupLoading
              ? Array.from({ length: 3 }).map((_, idx) => (
                  <Skeleton key={idx} className="min-h-[66px]"></Skeleton>
                ))
              : productAgeGroups?.docs?.map((ageGroup) => (
                  <div
                    onClick={() => handleSelectCategory(ageGroup, 'age')}
                    key={ageGroup.id}
                    className={`typo-body-6 $ flex min-h-14 cursor-pointer items-center justify-center rounded-lg border ${selectedCategories[ageGroup.id] ? 'border-primary-500 bg-white text-primary-500' : 'border-transparent bg-neutral-200'}`}
                  >
                    {ageGroup.title}
                  </div>
                ))}
          </div>
        </div>

        {/* Loại sản phẩm */}
        <div className="">
          <div className="typo-body-3">{t('MES-708')}</div>

          <div className="mt-4 grid grid-cols-3 gap-3">
            {isGetMedicineTypeLoading
              ? Array.from({ length: 3 }).map((_, idx) => (
                  <Skeleton key={idx} className="min-h-[66px]"></Skeleton>
                ))
              : medicineType?.docs?.map((medicineType) => (
                  <div
                    onClick={() => handleSelectCategory(medicineType, 'medicine')}
                    key={medicineType.id}
                    className={`typo-body-6 $ flex min-h-14 cursor-pointer items-center justify-center rounded-lg border ${selectedCategories[medicineType.id] ? 'border-primary-500 bg-white text-primary-500' : 'border-transparent bg-neutral-200'}`}
                  >
                    {medicineType.title}
                  </div>
                ))}
          </div>
        </div>

        {/* Note */}
        {medicineType && medicineType?.docs?.length && (
          <div className="rounded-lg bg-informative-100 p-3">
            <div
              onClick={() => setIsExpandNote(!isExpandNote)}
              className="flex cursor-pointer items-center justify-between gap-3"
            >
              <div className="flex items-end gap-3 text-primary-500">
                <Image src={NoteIcon} alt="note" className="size-5" />
                {t('MES-719')}
              </div>

              <Image src={arrowDown} alt="arrow" className="size-5" />
            </div>

            {isExpandNote && (
              <div className="mt-3 flex flex-col gap-3">
                {medicineType.docs.reverse().map((item) => {
                  const icon = item.icon as Media
                  const iconURL = icon?.url || icon?.thumbnailURL || ''
                  return (
                    <div key={item.id} className="flex flex-col gap-2">
                      <div className="flex items-center gap-2">
                        {iconURL && (
                          <div className="relative h-[24px] w-[24px]">
                            <Image
                              alt={'logo'}
                              src={iconURL}
                              width={24}
                              height={24}
                              className="h-full w-full object-cover"
                            />
                          </div>
                        )}
                        <span className="typo-body-3">{item.title}</span>
                      </div>
                      <div className="typo-body-4">{item?.note ?? '-'}</div>
                    </div>
                  )
                })}
              </div>
            )}
          </div>
        )}
      </div>

      <div className="flex items-center justify-end gap-3 pt-6">
        <Button
          onClick={() => handleApplyFilter('default')}
          variant={'outline'}
          className="typo-button-3 border-custom-neutral-200 px-4 py-2"
        >
          {t('MES-105')}
        </Button>
        <Button
          onClick={() => handleApplyFilter('filter')}
          variant={'default'}
          className="typo-button-3 px-4 py-2"
        >
          {t('MES-281')}
        </Button>
      </div>
    </div>
  )
}

export default FilterModal
