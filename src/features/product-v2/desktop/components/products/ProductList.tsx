import { useTranslations } from 'next-intl'
import ProductItem from './ProductItem'
import { Product } from '@/payload-types'
import ProductSkeleton from './ProductSkeleton'
import Image from 'next/image'

import FilterIcon from '@/assets/icons/setting-4.svg'
import FilterModal, { IFilterProduct } from '../modals/FilterModal'
import { useDialog } from '@/hooks/common/useDialog'
import React from 'react'

import CloseIcon from '@/assets/icons/close_red.svg'
import clearIcon from '@/assets/icons/exit.svg'

type ProductListType = {
  productListData: Product[]
  isLoading: boolean
  totalData: number
  applyFilter: (data: IFilterProduct) => void
  filterOptions: IFilterProduct['params']
  isMedicine?: boolean
}
const ProductList: React.FC<ProductListType> = ({
  productListData,
  totalData,
  isLoading,
  applyFilter,
  filterOptions,
  isMedicine = true,
}) => {
  const t = useTranslations()
  const { openDialog } = useDialog()

  const handleCloseModal = (closeFnc: () => void) => {
    closeFnc()
  }

  const handleApplyFilter = (data: IFilterProduct, closeFnc: () => void) => {
    applyFilter(data)
    closeFnc()
  }

  const handleResetFilter = () => {
    applyFilter({ params: [], type: 'filter' })
  }

  const deleteFilter = (id: string) => {
    const newFilterOptions = filterOptions.filter((item) => item.id !== id)
    applyFilter({ params: newFilterOptions, type: 'filter' })
  }

  const handleOpenFilterPopup = () => {
    openDialog({
      children: ({ close }) => (
        <FilterModal
          isMedicine={isMedicine}
          applyFilter={(item) => handleApplyFilter(item, close)}
          close={() => handleCloseModal(close)}
          filterOptions={filterOptions}
        />
      ),
      variant: 'blank',
      wrapperClassName:
        'mobile-wrapper z-[1000] !max-w-[800px] !w-[734px] !max-h-[calc(100vh - 280px)] overflow-y-auto sm:min-w-0 rounded-xl',
    })
  }

  return isLoading ? (
    <ProductSkeleton />
  ) : (
    <>
      <div className="typo-body-3 mt-3 flex items-center justify-between gap-3 pr-2">
        <span className="typo-body-3">
          {t('MES-660')} ({totalData})
        </span>
        <div
          onClick={handleOpenFilterPopup}
          className={`typo-body-6 relative flex cursor-pointer items-center gap-2 text-primary-500 ${!!filterOptions.length ? 'after:absolute after:-right-1 after:top-0 after:h-2 after:w-2 after:rounded-full after:bg-red-500' : ''} `}
        >
          {t('MES-481')}
          <Image src={FilterIcon} alt="filter icon" className="size-4" />
        </div>
      </div>

      {!!filterOptions.length && (
        <div className="flex items-center justify-between gap-3">
          <div className="flex items-center gap-3">
            <span className="typo-body-7 text-subdued">
              {t('MES-744')} ({filterOptions.length}) :
            </span>
            <div className="mt-3 flex flex-wrap gap-2">
              {filterOptions.map((item) => (
                <div
                  key={item.id}
                  className="typo-body-6 flex cursor-pointer items-center gap-2 rounded-lg border border-primary-500 bg-white px-3 py-2 text-primary-500"
                >
                  {item.title}
                  <Image
                    onClick={() => deleteFilter(item.id)}
                    src={clearIcon}
                    alt={'clear'}
                    className="size-5 cursor-pointer"
                  />
                </div>
              ))}
            </div>
          </div>

          <div
            className="typo-body-6 flex cursor-pointer items-center gap-2 text-danger-700"
            onClick={handleResetFilter}
          >
            {t('MES-709')}

            <Image src={CloseIcon} alt={'close'} className="size-4" />
          </div>
        </div>
      )}

      <div className="mt-3">
        <div className="grid grid-cols-6 gap-3">
          {productListData.map((product) => {
            return (
              <div key={product.id}>
                <ProductItem productData={product} />
              </div>
            )
          })}
        </div>
      </div>
    </>
  )
}

export default ProductList
