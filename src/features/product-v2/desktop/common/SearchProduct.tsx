'use client'

import { Button } from '@/components/ui/Button/Button'
import { Input } from '@/components/ui/Input/Input'
import { useTranslations } from 'next-intl'
import Image from 'next/image'

import SearchIcon from '@/assets/icons/search-normal.svg'
import CircleClose from '@/assets/icons/close-circle-gray-filled.svg'
import CameraIcon from '@/assets/icons/camera-icon.svg'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useDialog } from '@/hooks/common/useDialog'
import ResearchByImage from './ResearchByImage'

type SearchProductProps = {
  value: string
  onChange: (value: string) => void
}

const SearchProduct: React.FC<SearchProductProps> = ({ onChange, value }) => {
  const t = useTranslations()
  const router = useRouter()
  const [inputValue, setInputValue] = useState<string>('')

  const { openDialog } = useDialog()

  useEffect(() => {
    setInputValue(value)
  }, [value])

  const handleOpenCamera = () => {
    openDialog({
      children: () => <ResearchByImage />,
      variant: 'blank',
      wrapperClassName:
        'mobile-wrapper z-[1000] !h-[353px] !min-w-[826px] overflow-y-auto sm:min-w-0',
    })
  }

  const handleClearSearch = () => {
    setInputValue('')
    router.push('/desktop/medicines')
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value)
    setInputValue(e.target.value)
  }

  return (
    <div className="flex items-center gap-3">
      <div className="relative w-full">
        <Input
          placeholder={t('MES-66')}
          className="w-full rounded-lg px-4 py-2 pr-6"
          value={inputValue ?? ''}
          onChange={handleInputChange}
        />
        <Image
          alt={inputValue ? 'clear search' : 'search'}
          src={inputValue ? CircleClose : SearchIcon}
          width={20}
          height={20}
          className="absolute right-3 top-1/2 -translate-y-1/2 cursor-pointer"
          onClick={inputValue ? handleClearSearch : undefined}
        />
      </div>

      <Button onClick={handleOpenCamera} variant={'blank'} className="p-0">
        <Image src={CameraIcon} alt="camera" width={32} height={32} />
      </Button>
    </div>
  )
}

export default SearchProduct
