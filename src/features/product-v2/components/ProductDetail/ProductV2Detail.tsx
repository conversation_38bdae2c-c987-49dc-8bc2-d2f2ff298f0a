'use client'
import { DisclaimerWarning } from '@/components/DisclaimerWarning/DisclaimerWarning'
import { InformationIcon } from '@/components/Icons/InformationIcon'

import { PreviousPageButton } from '@/components/PreviousPageButton/PreviousPageButton'
import { ShareLinkButton } from '@/components/ShareLinkButton/ShareLinkButton'
import { Media, MedicineBuyButton, Product } from '@/payload-types'
import { cn } from '@/utilities/cn'
import { SerializedEditorState } from '@payloadcms/richtext-lexical/lexical'

import { useTranslations } from 'next-intl'
import Image from 'next/image'
import React, { useEffect } from 'react'
import { ProductV2AdditionalInfo } from './ProductV2AdditionalInfo'
import { ProductV2Content } from './ProductV2Content'
import { MedStore, ProductV2Store } from './ProductV2Store'
import { FavoriteProductButtonClient } from './FavoriteProductButton.client'
import { ProductV2 } from '../../types'
import { ProductV2RelatedPosts } from './ProductV2RelatedPosts'
import { SCROLL_TO_TOP_BUTTON_ID } from '@/constants/global.constant'
import { ProuductV2SearchFaculty } from './ProuductV2SearchFaculty'
interface ProductV2DetailProps {
  product: Product
  isNative?: boolean
}

export const ProductV2Detail: React.FC<ProductV2DetailProps> = ({ product, isNative }) => {
  const {
    title,
    heroImage,
    contraindications,
    dosage,
    ingredient,
    stores,
    id,
    specification,
    dosageForm,
    uses,
    note,
    isFavorite,
    jaTitle,
  } = (product as ProductV2) || {}
  const { url, width, height } = (heroImage as Media) || {}

  const filteredStores = (stores as MedStore[]).filter((store) => {
    const medicineStore = store?.['medicine-store'] as unknown as MedicineBuyButton
    return store.url && medicineStore?.id && medicineStore?.title
  })

  const t = useTranslations()

  useEffect(() => {
    setTimeout(() => {
      if (filteredStores.length > 0) {
        const scrollToTopButton = document.getElementById(SCROLL_TO_TOP_BUTTON_ID)
        if (scrollToTopButton) {
          scrollToTopButton.style.bottom = '128px'
        }
      }
    }, 1000)
  }, [filteredStores])

  return (
    <div className={cn('flex flex-col gap-4 p-4', filteredStores.length > 0 && 'pb-32')}>
      {!isNative && (
        <div className="flex h-[28px] items-center justify-between">
          <PreviousPageButton
            fallbackURL="/products/medicines"
            isNative={isNative}
          ></PreviousPageButton>
        </div>
      )}

      {/* banner and title */}
      <article key="banner" className="flex flex-col gap-3 rounded-lg bg-white">
        {url && (
          <div
            className={cn('relative max-h-[360px] w-full overflow-hidden rounded-lg')}
            style={{
              paddingTop:
                height && width
                  ? `${Math.min((height / width) * 100, 100)}%` // Limit max to 100%
                  : '56.25%', // fallback (16:9)
            }}
          >
            <Image
              src={url}
              alt={title || ''}
              fill
              className="absolute left-0 top-0 h-full w-full object-contain"
              sizes="400px"
            />
          </div>
        )}

        <div className="flex items-start justify-between gap-2">
          <div className="flex flex-col gap-1">
            <p className="typo-body-3">{title}</p>
            {jaTitle && <p className="typo-body-6 text-subdued">{jaTitle}</p>}
          </div>
          <div className="flex shrink-0 items-center gap-1">
            <ShareLinkButton
              className="shrink-0"
              title={title || 'Medicine'}
              text={title || 'Medicine'}
            />
            <FavoriteProductButtonClient
              id={id}
              isFavorite={isFavorite ?? false}
            ></FavoriteProductButtonClient>
          </div>
        </div>
      </article>

      {/* Disclaimer */}
      <DisclaimerWarning></DisclaimerWarning>

      {/* Additional Info */}
      <ProductV2AdditionalInfo product={product} />

      {/* Search Faculty */}
      <ProuductV2SearchFaculty isNative={isNative} />

      {/* Stores */}
      {filteredStores.length > 0 && <ProductV2Store filteredStores={filteredStores} />}

      <article key="detail-info" className="flex flex-col gap-3 rounded-lg bg-white p-3">
        <div className="flex items-center gap-3">
          <InformationIcon className="shrink-0"></InformationIcon>

          <div className="typo-body-3">{t('MES-490')}</div>
        </div>

        <ProductV2Content
          uses={uses as SerializedEditorState}
          dosage={dosage as SerializedEditorState}
          dosageForm={dosageForm as SerializedEditorState}
          specification={specification as SerializedEditorState}
          ingredient={ingredient as SerializedEditorState}
          contraindications={contraindications as SerializedEditorState}
          note={note as SerializedEditorState}
        />
      </article>
      <ProductV2RelatedPosts />
    </div>
  )
}
