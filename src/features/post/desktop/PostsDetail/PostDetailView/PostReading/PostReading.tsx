import { PreviousPageButton } from '@/components/PreviousPageButton/PreviousPageButton'
import { ShareIcon, ShareLinkButton } from '@/components/ShareLinkButton/ShareLinkButton'
import { Media, Post } from '@/payload-types'
import { cn } from '@/utilities/cn'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import React from 'react'

import ClockIcon from '@/assets/icons/clock-primary.svg'
import RichText from '@/components/RichText'
import { dateToYMD } from '@/utilities/date'

type PostReading = {
  post: Post
}
const PostReading: React.FC<PostReading> = ({ post }) => {
  const t = useTranslations()

  const isShowAdSense = true

  const { title, heroImage, content, createdAt } = post || {}
  const { sizes } = (heroImage as Media) || {}

  const checkIsIn24Hour = (date: string | Date): string => {
    const now = new Date()
    const targetDate = new Date(date)

    const diffMs = now.getTime() - targetDate.getTime()

    if (diffMs < 0) return ''

    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))

    if (diffHours >= 24) {
      return ''
    }

    if (diffMinutes < 1) {
      return `1 ${t('MES-737')}`
    }

    if (diffMinutes < 60) {
      return `${diffMinutes} ${t('MES-737')}`
    }

    return `${diffHours} ${t('MES-738')}`
  }

  return (
    <React.Fragment>
      <div className="flex items-center justify-between px-4 pt-4">
        <PreviousPageButton fallbackURL="/desktop/news" isNative={false}></PreviousPageButton>
        <ShareLinkButton title={title} text={title} className="ml-auto">
          <div className="flex items-center gap-3 rounded-[99px] bg-neutral-100 px-3 py-2 text-primary-500">
            <ShareIcon />
            {t('MES-444')}
          </div>
        </ShareLinkButton>
      </div>
      <article className="flex flex-col gap-y-4 p-4">
        {/* Image */}
        {sizes?.thumbnail?.url && (
          <div className={cn('relative h-[365px] w-full overflow-hidden rounded-lg')}>
            <Image
              src={sizes?.thumbnail?.url}
              alt={title || ''}
              fill
              className="h-full w-full object-cover"
              sizes="400px"
            ></Image>
          </div>
        )}
        <h1 className="typo-heading-7">{title}</h1>

        {createdAt && (
          <div className="flex items-center gap-2">
            <Image src={ClockIcon} alt={'clock'} width={16} height={16} />
            <time className="typo-body-8 text-cyan-600">{dateToYMD(new Date(createdAt))}</time>

            {checkIsIn24Hour(createdAt) && (
              <React.Fragment>
                <div className="h-1 w-1 rounded-[100%] bg-cyan-600"></div>
                <span className="typo-body-8 text-cyan-600">{checkIsIn24Hour(createdAt)}</span>
              </React.Fragment>
            )}
          </div>
        )}

        <div className="space-y-4">
          {/* Content */}
          {content && (
            <RichText className="" data={content} enableGutter={false} showADS={isShowAdSense} />
          )}
        </div>
      </article>
    </React.Fragment>
  )
}

export default PostReading
