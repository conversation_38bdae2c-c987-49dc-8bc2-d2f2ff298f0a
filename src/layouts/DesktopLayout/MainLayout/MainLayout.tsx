import FeatureList from '@/features/home/<USER>/Home/FeatureList'
import HomeCard from '@/features/home/<USER>/Home/HomeCard'
import { HomeDailyVocabulary } from '@/features/home/<USER>/Home/HomeDailyVocabulary/HomeDailyVocabulary'
import { HomeFaculties } from '@/features/home/<USER>/Home/HomeFaculties/HomeFaculties'
import { HomeMedicines } from '@/features/home/<USER>/Home/HomeMedicines/HomeMedicines'
import { HomePosts } from '@/features/home/<USER>/Home/HomePosts/HomePosts'
import ResearchInfo from '@/features/home/<USER>/Home/ResearchInfo'
import SearchMedicineByImage from '@/features/home/<USER>/Home/SearchMedicineByImage'
import ReCaptcha from '@/components/ReCaptcha/ReCaptcha'
import { HomeDidYouKnow } from '@/features/home/<USER>/HomeDidYouKnow/HomeDidYouKnow'
import { HomeSubscribe } from '@/features/home/<USER>/HomeSubscribe/HomeSubscribe'
import {
  getBodyPartsCache,
  getHomeFacultiesCache,
  getNewestVersionCache,
  getRandomFactsCache,
  getRandomKeywordsCache,
  getRandomSearchTipsCache,
  getSubscriptionPlansCache,
} from '@/features/home/<USER>/Home/HomeContainer'
import { getLocale } from 'next-intl/server'

const MainLayout: React.FC = async () => {
  const locale = await getLocale()

  const [_, faculties, bodyParts, __, randomKeywords, randomFacts, randomSearchTips] =
    await Promise.all([
      getNewestVersionCache(),
      getHomeFacultiesCache(locale),
      getBodyPartsCache(locale),
      getSubscriptionPlansCache(),
      getRandomKeywordsCache(),
      getRandomFactsCache(locale),
      getRandomSearchTipsCache(locale),
    ])

  return (
    <div className="flex max-w-full gap-6 overflow-hidden bg-custom-background-hover px-16 py-6">
      <div className="inline-flex min-w-0 flex-[7] flex-col gap-3 rounded-3xl bg-white p-4">
        <ResearchInfo searchTips={randomSearchTips?.docs ?? []} />

        <FeatureList />

        <HomeFaculties faculties={faculties?.docs ?? []} />

        <HomeMedicines bodyParts={bodyParts ?? null}></HomeMedicines>

        <HomePosts />

        <HomeCard />
      </div>

      <div className="inline-flex h-fit min-w-0 flex-[3] flex-col gap-3 rounded-3xl bg-white p-4">
        <SearchMedicineByImage />

        <HomeDidYouKnow classStyleSection="px-0" fact={randomFacts?.docs?.[0] ?? null} />

        <HomeDailyVocabulary keywords={randomKeywords?.docs ?? []}></HomeDailyVocabulary>

        <ReCaptcha>
          <HomeSubscribe classStyleSection={'p-0 pb-2'} />
        </ReCaptcha>
      </div>
    </div>
  )
}

export default MainLayout
